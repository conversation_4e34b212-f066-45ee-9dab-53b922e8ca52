# HeliosETL - Implementation TODOs

## Overview
This document tracks the missing implementations and issues found during the review of Models and Repositories.

## Critical Issues Found

### 1. Mission Model and Repository Issues ❌
**Priority: HIGH**

**Problem**: 
- MissionRepository doesn't populate base Activite properties (Oid, __version, Code, Libelle, Description, dates, etc.)
- Add, Update, Delete methods are incomplete or throw NotImplementedException
- GetAll and GetById only populate Mission-specific properties, ignoring inherited Activite properties

**Impact**: 
- Mission entities are incomplete when loaded from database
- Cannot perform CRUD operations on Mission entities
- Data integrity issues

**Files Affected**:
- `Repositories/v2/MissionRepository.cs`
- `Models/v2/Mission.cs` (verification needed)

### 2. Repository Implementation Completeness ⚠️
**Priority: MEDIUM**

**Status**: Need to verify all repositories have complete CRUD implementations

**Files to Review**:
- All repositories in `Repositories/v2/`
- Ensure all methods properly implement the IRepository interface

## Implementation Plan

### Phase 1: Fix Mission Repository (HIGH PRIORITY)
1. ✅ **Identify Issues**: Mission repository incomplete
2. ⏳ **Fix GetAll Method**: Populate all Activite base properties
3. ⏳ **Fix GetById Method**: Populate all Activite base properties  
4. ⏳ **Implement Add Method**: Complete implementation
5. ⏳ **Implement Update Method**: Complete implementation
6. ⏳ **Implement Delete Method**: Complete implementation
7. ⏳ **Test Repository**: Verify all CRUD operations work

### Phase 2: Verify Other Repositories (MEDIUM PRIORITY)
1. ⏳ **Review All v2 Repositories**: Check for similar issues
2. ⏳ **Fix Any Incomplete Implementations**: Complete missing methods
3. ⏳ **Update Documentation**: Reflect actual implementation status

### Phase 3: Documentation Updates (LOW PRIORITY)
1. ⏳ **Update Project Structure**: Reflect current implementation
2. ⏳ **Document Known Issues**: Any remaining limitations
3. ⏳ **Update README**: Implementation status

## Detailed Issues

### MissionRepository.cs Specific Problems:

1. **GetAll Method Issues**:
   ```csharp
   // Current - Only populates Mission properties
   Mission mission = new Mission()
   {
       TypeMission = GetTypeMissionForMission(...),
       ParentMission = GetParentMissionForMission(...),
       SubMissions = GetSubMissionsForMission(...)
   };
   
   // Missing: Oid, __version, Code, Libelle, Description, DateCreation, etc.
   ```

2. **Add Method Issues**:
   ```csharp
   // Current - Commented out and returns false
   return false;
   
   // Needs: Complete INSERT implementation with all Activite properties
   ```

3. **Update Method Issues**:
   ```csharp
   // Current - Missing Oid property access
   // command.Parameters.AddWithValue("@oid", mission.oid); // Commented out
   
   // Needs: Access to inherited Oid property from Activite
   ```

4. **Delete Method Issues**:
   ```csharp
   // Current - Throws NotImplementedException
   throw new NotImplementedException("Delete requires Mission to have an oid property for identification");
   
   // Needs: Use inherited Oid property from Activite base class
   ```

## Next Steps

1. **Immediate**: Fix MissionRepository implementation
2. **Short-term**: Review other repositories for similar issues
3. **Long-term**: Comprehensive testing of all CRUD operations

## Notes

- Mission inherits from Activite which has Oid property - repository should use this
- All v2 entities should follow consistent patterns for CRUD operations
- Database schema should match model structure
