# HeliosETL Configuration Example
# Copy this file to config.yaml and fill in your actual values

database:
  sqlServer:
    dataSource: "your-sql-server-host"
    initialCatalog: "your-database-name"
    userID: "your-username"
    password: "your-password"

  mySQL:
    server: "your-mysql-server-host"
    database: "your-mysql-database-name"
    userID: "your-mysql-username"
    password: "your-mysql-password"

logging:
  console:
    enabled: false               # Console logging disabled - using Spectre.Console for display
    minimumLevel: "Information"  # Verbose, Debug, Information, Warning, Error, Fatal

  file:
    enabled: true
    minimumLevel: "Information"  # All log levels to files (change to "Error" for error-only)
    logDirectory: "logs"
    fileNameTemplate: "helios-etl-{Date}.log"
    retainedFileCountLimit: 30   # Keep 30 days of logs
    fileSizeLimitBytes: 10485760 # 10MB per file
