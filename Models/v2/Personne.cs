namespace HeliosETL.Models.v2;

/// <summary>
/// Extends: ExternalEntity
/// ComposedOff : TypePersonne(Extends: AbstractListeValeur)
/// </summary>
public class Personne : ExternalEntity
{
    public string Nom { get; set; } = string.Empty;
    public string Prenom { get; set; } = string.Empty;
    public string Fonction { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Telephone { get; set; } = string.Empty;
    public string Mobile { get; set; } = string.Empty;
    public TypePersonne TypePersonne { get; set; }
}