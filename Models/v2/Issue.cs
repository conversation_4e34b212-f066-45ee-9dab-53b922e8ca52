namespace HeliosETL.Models.v2;

public class Issue
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = String.Empty;
    public string Sujet { get; set; } = String.Empty;
    public string Description { get; set; } = String.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleDebut { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleFin { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveDebut { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveFin { get; set; } = DateTime.MinValue;
    public IssuePriorite Priorite { get; set; }
    public IssueStatut Statut { get; set; }
    public Activite Activite { get; set; }
    public string KindOfActivite { get; set; } = String.Empty;
    public Issue IssueParente { get; set; }
    public string KindOfIssueParente { get; set; } = String.Empty;
    public HashSet<Issue> Issues { get; set; } = new HashSet<Issue>();
    public byte Avancement { get; set; }
    public int TempsEstimeMinutes { get; set; }
    public int TempsEffectifMinutes { get; set; }
    public HashSet<Issue> RelationsSortantes { get; set; } = new HashSet<Issue>();
    public HashSet<Issue> RelationsEntrantes { get; set; } = new HashSet<Issue>();
    public HashSet<Journal> Journaux { get; set; } = new HashSet<Journal>();
}