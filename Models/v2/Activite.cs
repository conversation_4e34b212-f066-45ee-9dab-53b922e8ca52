namespace HeliosETL.Models.v2;

public abstract class Activite
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleDebut { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleFin { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveDebut { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveFin { get; set; } = DateTime.MinValue;
    public DomaineMetier DomainePrincipal { get; set; }
    public HashSet<DomaineMetier> Domaines { get; set; } = new HashSet<DomaineMetier>();
}