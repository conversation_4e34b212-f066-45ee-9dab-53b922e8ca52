using HeliosETL.Models.v1;
using HeliosETL.Models.v2;
using HeliosETL.Repositories.v1;
using HeliosETL.Repositories.v2;
using Serilog;

namespace HeliosETL.Services;

public class CachedMemory
{
    private static CachedMemory _instance;
    private static readonly ILogger _logger = LoggingService.Instance.GetLogger<CachedMemory>();
    
    /*******************************************************************************************
     * V1
     ******************************************************************************************/
    /// <summary>
    /// [OLD] map avec Issue
    /// </summary>
    public HashSet<Tickets> Tickets { get; set; } = new HashSet<Tickets>();
    
    /// <summary>
    /// [OLD] map avec Journal
    /// </summary>
    public HashSet<TicketsHistorique> TicketsHistorique { get; set; } = new HashSet<TicketsHistorique>();
    
    /// <summary>
    /// [OLD] map avec Mission
    /// </summary>
    public HashSet<Categorie> Categories { get; set; } = new HashSet<Categorie>();
    
    /// <summary>
    /// [OLD] map avec Personne
    /// </summary>
    public HashSet<Contacts> Contacts { get; set; } = new HashSet<Contacts>();
    
    /*******************************************************************************************
     * V2
     ******************************************************************************************/
    /// <summary>
    /// [NEW] map avec Tickets
    /// </summary>
    public HashSet<Issue> Issues { get; set; } = new HashSet<Issue>();
    
    /// <summary>
    /// [NEW] map avec TicketsHistorique
    /// </summary>
    public HashSet<Journal> Journaux { get; set; } = new HashSet<Journal>();
    
    /// <summary>
    /// [NEW] map avec Contacts
    /// </summary>
    public HashSet<Personne> Personnes { get; set; } = new HashSet<Personne>();
    
    /// <summary>
    /// [NEW] map avec Categorie
    /// </summary>
    public HashSet<DomaineMetier> DomainesMetier { get; set; } = new HashSet<DomaineMetier>();
    
    /// <summary>
    /// [NEW] Map avec Type (Ticket) ?
    /// </summary>
    public HashSet<Activite> Activites { get; set; } = new HashSet<Activite>();
    
    /// <summary>
    /// [NEW] Map avec Categorie
    /// </summary>
    public HashSet<Mission> Missions { get; set; } = new HashSet<Mission>();
    
    /// <summary>
    /// [NEW] Map avec Priorite (Ticket)
    /// </summary>
    public HashSet<IssuePriorite> IssuePriorites { get; set; } = new HashSet<IssuePriorite>();
    
    /// <summary>
    /// [NEW] Map avec TypeMission (Mission)
    /// </summary>
    public HashSet<TypeMission> TypeMissions { get; set; } = new HashSet<TypeMission>();

    /// <summary>
    /// [NEW] Map avec Commanditaire
    /// </summary>
    public HashSet<Commanditaire> Commanditaires { get; set; } = new HashSet<Commanditaire>();

    /// <summary>
    /// [NEW] Map avec IssueOrigine
    /// </summary>
    public HashSet<IssueOrigine> IssueOrigines { get; set; } = new HashSet<IssueOrigine>();

    /// <summary>
    /// [NEW] Map avec IssueStatut
    /// </summary>
    public HashSet<IssueStatut> IssueStatuts { get; set; } = new HashSet<IssueStatut>();

    /// <summary>
    /// [NEW] Map avec JournalDetails
    /// </summary>
    public HashSet<JournalDetails> JournalDetails { get; set; } = new HashSet<JournalDetails>();

    /// <summary>
    /// [NEW] Map avec NiveauComplexite
    /// </summary>
    public HashSet<NiveauComplexite> NiveauxComplexite { get; set; } = new HashSet<NiveauComplexite>();

    /// <summary>
    /// [NEW] Map avec TypeDocument
    /// </summary>
    public HashSet<TypeDocument> TypeDocuments { get; set; } = new HashSet<TypeDocument>();

    /// <summary>
    /// [NEW] Map avec TypeProjet
    /// </summary>
    public HashSet<TypeProjet> TypeProjets { get; set; } = new HashSet<TypeProjet>();
    
    public static CachedMemory Get()
    {
        if (_instance == null)
        {
            _instance = new CachedMemory();
        }
        return _instance;
    }

    public static void LoadCache()
    {
        _logger.Information("Starting cache loading process...");

        try
        {
            var instance = Get();

            // Load V1 data (SQL Server)
            LoadV1Data(instance);

            // Load V2 data (MySQL)
            LoadV2Data(instance);

            _logger.Information("Cache loading completed successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load cache data");
            throw;
        }
    }

    private static void LoadV1Data(CachedMemory instance)
    {
        _logger.Debug("Loading V1 data from SQL Server...");

        try
        {
            // Load Tickets
            var ticketsRepo = new TicketsRepository();
            instance.Tickets = ticketsRepo.GetAll<Tickets>();
            _logger.Debug($"Loaded {instance.Tickets.Count} tickets");

            // Load TicketsHistorique
            var ticketsHistoriqueRepo = new TicketHistoriqueRepository();
            instance.TicketsHistorique = ticketsHistoriqueRepo.GetAll<TicketsHistorique>();
            _logger.Debug($"Loaded {instance.TicketsHistorique.Count} tickets historique");

            // Load Categories
            var categorieRepo = new CategorieRepository();
            instance.Categories = categorieRepo.GetAll<Categorie>();
            _logger.Debug($"Loaded {instance.Categories.Count} categories");

            // Load Contacts
            var contactsRepo = new ContactsRepository();
            instance.Contacts = contactsRepo.GetAll<Contacts>();
            _logger.Debug($"Loaded {instance.Contacts.Count} contacts");

            _logger.Information("V1 data loading completed");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load V1 data");
            throw;
        }
    }

    private static void LoadV2Data(CachedMemory instance)
    {
        _logger.Debug("Loading V2 data from MySQL...");

        try
        {
            // Load Issues
            var issueRepo = new IssueRepository();
            instance.Issues = issueRepo.GetAll<Issue>();
            _logger.Debug($"Loaded {instance.Issues.Count} issues");

            // Load Journaux
            var journalRepo = new JournalRepository();
            instance.Journaux = journalRepo.GetAll<Journal>();
            _logger.Debug($"Loaded {instance.Journaux.Count} journaux");

            // Load Personnes
            var personneRepo = new PersonneRepository();
            instance.Personnes = personneRepo.GetAll<Personne>();
            _logger.Debug($"Loaded {instance.Personnes.Count} personnes");

            // Load DomainesMetier
            var domaineMetierRepo = new DomaineMetierRepository();
            instance.DomainesMetier = domaineMetierRepo.GetAll<DomaineMetier>();
            _logger.Debug($"Loaded {instance.DomainesMetier.Count} domaines métier");

            // Load Missions
            var missionRepo = new MissionRepository();
            instance.Missions = missionRepo.GetAll<Mission>();
            _logger.Debug($"Loaded {instance.Missions.Count} missions");

            // Load IssuePriorites
            var issuePrioriteRepo = new IssuePrioriteRepository();
            instance.IssuePriorites = issuePrioriteRepo.GetAll<IssuePriorite>();
            _logger.Debug($"Loaded {instance.IssuePriorites.Count} issue priorités");

            // Load TypeMissions
            var typeMissionRepo = new TypeMissionRepository();
            instance.TypeMissions = typeMissionRepo.GetAll<TypeMission>();
            _logger.Debug($"Loaded {instance.TypeMissions.Count} type missions");

            // Load Commanditaires
            var commanditaireRepo = new CommanditaireRepository();
            instance.Commanditaires = commanditaireRepo.GetAll<Commanditaire>();
            _logger.Debug($"Loaded {instance.Commanditaires.Count} commanditaires");

            // Load IssueOrigines
            var issueOrigineRepo = new IssueOrigineRepository();
            instance.IssueOrigines = issueOrigineRepo.GetAll<IssueOrigine>();
            _logger.Debug($"Loaded {instance.IssueOrigines.Count} issue origines");

            // Load IssueStatuts
            var issueStatutRepo = new IssueStatutRepository();
            instance.IssueStatuts = issueStatutRepo.GetAll<IssueStatut>();
            _logger.Debug($"Loaded {instance.IssueStatuts.Count} issue statuts");

            // Load JournalDetails
            var journalDetailsRepo = new JournalDetailsRepository();
            instance.JournalDetails = journalDetailsRepo.GetAll<JournalDetails>();
            _logger.Debug($"Loaded {instance.JournalDetails.Count} journal details");

            // Load NiveauxComplexite
            var niveauComplexiteRepo = new NiveauComplexiteRepository();
            instance.NiveauxComplexite = niveauComplexiteRepo.GetAll<NiveauComplexite>();
            _logger.Debug($"Loaded {instance.NiveauxComplexite.Count} niveaux complexité");

            // Load TypeDocuments
            var typeDocumentRepo = new TypeDocumentRepository();
            instance.TypeDocuments = typeDocumentRepo.GetAll<TypeDocument>();
            _logger.Debug($"Loaded {instance.TypeDocuments.Count} type documents");

            // Load TypeProjets
            var typeProjetRepo = new TypeProjetRepository();
            instance.TypeProjets = typeProjetRepo.GetAll<TypeProjet>();
            _logger.Debug($"Loaded {instance.TypeProjets.Count} type projets");

            // TODO: Load Activites - ActiviteRepository not implemented yet (Activite is abstract)
            _logger.Warning("ActiviteRepository not implemented - Activites collection will remain empty (Activite is abstract, use Mission instead)");

            _logger.Information("V2 data loading completed");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load V2 data");
            throw;
        }
    }
}