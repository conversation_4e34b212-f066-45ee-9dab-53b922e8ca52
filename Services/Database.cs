using System.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Data.Sqlite;
using MySql.Data.MySqlClient;
using HeliosETL.Models.Configuration;
using Serilog;

namespace HeliosETL.Services;

public class Database
{
    public MySqlConnection MySQLConn { get; set; }
    public SqlConnection SqlServer { get; set; }
    public SqliteConnection Sqlite { get; set; }
    private DatabaseConfig _databaseConfig;
    private readonly ILogger _logger;

    public Database()
    {
        _logger = LoggingService.Instance.GetLogger<Database>();
        _logger.Debug("Initializing Database service");
    }

    public bool SetupDatabase()
    {
        try
        {
            _logger.Information("Setting up database connections");
            _databaseConfig = ConfigurationService.Instance.GetDatabaseConfig();
            this.MySQLConn = new MySqlConnection(this.getHeliosMySQLConnectionString());
            this.SqlServer = new SqlConnection(this.getHeliosSQLServerConnectionString());
            this.Sqlite = new SqliteConnection(this.getSqliteConnectionString());
            _logger.Information("Database connections setup successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to setup database connections");
            return false;
        }
    }

    public string getHeliosSQLServerConnectionString()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder();
        builder.DataSource = _databaseConfig.SqlServer.DataSource;
        builder.InitialCatalog = _databaseConfig.SqlServer.InitialCatalog;
        builder.UserID = _databaseConfig.SqlServer.UserID;
        builder.Password = _databaseConfig.SqlServer.Password;
        return builder.ConnectionString;
    }
    
    public string getHeliosMySQLConnectionString()
    {
        MySqlConnectionStringBuilder builder = new MySqlConnectionStringBuilder();
        builder.Server = _databaseConfig.MySQL.Server;
        builder.Database = _databaseConfig.MySQL.Database;
        builder.UserID = _databaseConfig.MySQL.UserID;
        builder.Password = _databaseConfig.MySQL.Password;
        return builder.ConnectionString;
    }
    
    public string getSqliteConnectionString()
    {
        SqliteConnectionStringBuilder builder = new SqliteConnectionStringBuilder();
        builder.DataSource = "ETL.db";
        return builder.ConnectionString;
    }

    /// <summary>
    /// Verification que la connexion avec la base SQL de donnees fonctionne correctement
    /// </summary>
    public bool testConnexion()
    {
        _logger.Information("Testing database connections");
        try
        {
            this.MySQLConn.Open();
            this.SqlServer.Open();
            _logger.Information("Database connection test successful");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Database connection test failed");
            return false;
        }
        finally
        {
            if (this.MySQLConn.State == ConnectionState.Open)
            {
                this.MySQLConn.Close();
            }
            if (this.SqlServer.State == ConnectionState.Open)
            {
                this.SqlServer.Close();
            }
        }

        return true;
    }
}