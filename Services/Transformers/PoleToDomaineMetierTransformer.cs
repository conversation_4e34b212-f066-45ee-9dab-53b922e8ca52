namespace HeliosETL.Services.Transformers;

public class PoleToDomaineMetierTransformer : ITransformer
{
    public HashSet<T> Extract<T>()
    {
        throw new NotImplementedException();
    }

    public T Prepare<T>()
    {
        throw new NotImplementedException();
    }

    public T Transform<T>()
    {
        throw new NotImplementedException();
    }

    public bool Validate()
    {
        throw new NotImplementedException();
    }

    public bool Load()
    {
        throw new NotImplementedException();
    }
}