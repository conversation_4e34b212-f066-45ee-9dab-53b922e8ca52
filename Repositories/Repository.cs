using HeliosETL.Services;
using Serilog;

namespace HeliosETL.Repositories;

public class Repository 
{
    protected Database _db;
    private readonly ILogger _logger;

    public Repository()
    {
        _db = new Database();
        _logger = LoggingService.Instance.GetLogger<Repository>();
        Init();
    }

    private void Init()
    {
        if (!_db.SetupDatabase())
        {
            _logger.Error("Failed to setup database connections");
            return;
        }
        _logger.Debug("Initializing Repository service");
        _logger.Information("Repository service initialized successfully");
    }
}