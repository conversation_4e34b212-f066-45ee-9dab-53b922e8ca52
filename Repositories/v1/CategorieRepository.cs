using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class CategorieRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }
            HashSet<Categorie> categories = new HashSet<Categorie>();

            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("SELECT libelle, idPole FROM Categories", this._db.SqlServer))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Categorie categorie = new Categorie()
                            {
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                IdPole = Convert.ToInt32(reader["idPole"])
                            };

                            categories.Add(categorie);
                        }
                    }
                }

                return categories.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }

            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("SELECT libelle, idPole FROM Categories WHERE idPole = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Categorie categorie = new Categorie()
                            {
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                IdPole = Convert.ToInt32(reader["idPole"])
                            };
                            return (T)(object)categorie;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var categorie = entity as Categorie;
            if (categorie == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.SqlServer.Open();
                string sql = @"INSERT INTO Categories (libelle, idPole) VALUES (@libelle, @idPole)";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@libelle", categorie.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@idPole", categorie.IdPole);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var categorie = entity as Categorie;
            if (categorie == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.SqlServer.Open();
                string sql = @"UPDATE Categories SET libelle=@libelle WHERE idPole=@idPole";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@libelle", categorie.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@idPole", categorie.IdPole);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }
            var categorie = entity as Categorie;
            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("DELETE FROM Categories WHERE idPole = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", categorie.IdPole);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Categorie))
            {
                throw new ArgumentException("Invalid type. Expected Categorie.");
            }
            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("DELETE FROM Categories WHERE idPole = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }
    }
}