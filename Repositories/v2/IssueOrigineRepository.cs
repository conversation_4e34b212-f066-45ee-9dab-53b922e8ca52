using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueOrigineRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            HashSet<IssueOrigine> issueOrigines = new HashSet<IssueOrigine>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssueOrigine", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IssueOrigine issueOrigine = new IssueOrigine()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForIssueOrigine(Convert.ToInt64(reader["oid"]))
                            };

                            issueOrigines.Add(issueOrigine);
                        }
                    }
                }

                return issueOrigines.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssueOrigine WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            IssueOrigine issueOrigine = new IssueOrigine()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForIssueOrigine(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)issueOrigine;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueOrigine = entity as IssueOrigine;
            if (issueOrigine == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO IssueOrigine (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    command.Parameters.AddWithValue("@__version", issueOrigine.__version);
                    command.Parameters.AddWithValue("@code", issueOrigine.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueOrigine.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueOrigine.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueOrigine.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueOrigine = entity as IssueOrigine;
            if (issueOrigine == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE IssueOrigine SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    command.Parameters.AddWithValue("@__version", issueOrigine.__version);
                    command.Parameters.AddWithValue("@code", issueOrigine.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueOrigine.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueOrigine.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueOrigine.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            var issueOrigine = entity as IssueOrigine;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssueOrigine WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssueOrigine WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForIssueOrigine(long oidIssueOrigine)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN IssueOrigine_DomaineMetier iodm ON dm.oid = iodm.oidDomaineMetier WHERE iodm.oidIssueOrigine = @oidIssueOrigine", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oidIssueOrigine", oidIssueOrigine);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
        }
    }
}
