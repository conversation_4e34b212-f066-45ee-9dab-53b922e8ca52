using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class MissionRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            HashSet<Mission> missions = new HashSet<Mission>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Mission", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Mission mission = new Mission()
                            {
                                // Activite base properties
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue,
                                DateModification = reader["dateModification"] != DBNull.Value ? Convert.ToDateTime(reader["dateModification"]) : DateTime.MinValue,
                                DatePrevisionnelleDebut = reader["datePrevisionnelleDebut"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleDebut"]) : DateTime.MinValue,
                                DatePrevisionnelleFin = reader["datePrevisionnelleFin"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleFin"]) : DateTime.MinValue,
                                DateEffectiveDebut = reader["dateEffectiveDebut"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveDebut"]) : DateTime.MinValue,
                                DateEffectiveFin = reader["dateEffectiveFin"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveFin"]) : DateTime.MinValue,
                                DomainePrincipal = GetDomaineMetierForMission(Convert.ToInt64(reader["oidDomainePrincipal"])),
                                Domaines = GetDomainesMetierForMission(Convert.ToInt64(reader["oid"])),

                                // Mission specific properties
                                TypeMission = GetTypeMissionForMission(Convert.ToInt64(reader["oidTypeMission"])),
                                ParentMission = GetParentMissionForMission(Convert.ToInt64(reader["oidParentMission"])),
                                SubMissions = GetSubMissionsForMission(Convert.ToInt64(reader["oid"]))
                            };

                            missions.Add(mission);
                        }
                    }
                }

                return missions.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Mission mission = new Mission()
                            {
                                // Activite base properties
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue,
                                DateModification = reader["dateModification"] != DBNull.Value ? Convert.ToDateTime(reader["dateModification"]) : DateTime.MinValue,
                                DatePrevisionnelleDebut = reader["datePrevisionnelleDebut"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleDebut"]) : DateTime.MinValue,
                                DatePrevisionnelleFin = reader["datePrevisionnelleFin"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleFin"]) : DateTime.MinValue,
                                DateEffectiveDebut = reader["dateEffectiveDebut"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveDebut"]) : DateTime.MinValue,
                                DateEffectiveFin = reader["dateEffectiveFin"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveFin"]) : DateTime.MinValue,
                                DomainePrincipal = GetDomaineMetierForMission(Convert.ToInt64(reader["oidDomainePrincipal"])),
                                Domaines = GetDomainesMetierForMission(Convert.ToInt64(reader["oid"])),

                                // Mission specific properties
                                TypeMission = GetTypeMissionForMission(Convert.ToInt64(reader["oidTypeMission"])),
                                ParentMission = GetParentMissionForMission(Convert.ToInt64(reader["oidParentMission"])),
                                SubMissions = GetSubMissionsForMission(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)mission;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private DomaineMetier GetDomaineMetierForMission(long oidDomaineMetier)
        {
            if (oidDomaineMetier <= 0) return null;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM DomaineMetier WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidDomaineMetier);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return null;
        }

        private HashSet<DomaineMetier> GetDomainesMetierForMission(long oidMission)
        {
            HashSet<DomaineMetier> domaines = new HashSet<DomaineMetier>();
            if (oidMission <= 0) return domaines;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                // TODO: This assumes a junction table Mission_DomaineMetier exists
                // If the relationship is stored differently, adjust the query accordingly
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN Mission_DomaineMetier mdm ON dm.oid = mdm.oidDomaineMetier WHERE mdm.oidMission = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidMission);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DomaineMetier domaine = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue
                            };
                            domaines.Add(domaine);
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return domaines;
        }

        private TypeMission GetTypeMissionForMission(long oidTypeMission)
        {
            if (oidTypeMission <= 0) return null;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM TypeMission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidTypeMission);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new TypeMission()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = new HashSet<DomaineMetier>() // Could be populated separately if needed
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return null;
        }

        private Mission GetParentMissionForMission(long oidParentMission)
        {
            if (oidParentMission <= 0) return null;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidParentMission);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Mission()
                            {
                                // Activite base properties
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue,
                                DateModification = reader["dateModification"] != DBNull.Value ? Convert.ToDateTime(reader["dateModification"]) : DateTime.MinValue,
                                DatePrevisionnelleDebut = reader["datePrevisionnelleDebut"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleDebut"]) : DateTime.MinValue,
                                DatePrevisionnelleFin = reader["datePrevisionnelleFin"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleFin"]) : DateTime.MinValue,
                                DateEffectiveDebut = reader["dateEffectiveDebut"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveDebut"]) : DateTime.MinValue,
                                DateEffectiveFin = reader["dateEffectiveFin"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveFin"]) : DateTime.MinValue,
                                DomainePrincipal = null, // Avoid deep recursion - load separately if needed
                                Domaines = new HashSet<DomaineMetier>(), // Empty to avoid deep recursion

                                // Mission specific properties
                                TypeMission = null, // Avoid deep recursion - load separately if needed
                                ParentMission = null, // Avoid deep recursion
                                SubMissions = new HashSet<Mission>() // Empty to avoid deep recursion
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return null;
        }

        private HashSet<Mission> GetSubMissionsForMission(long oidMission)
        {
            if (oidMission <= 0) return new HashSet<Mission>();

            HashSet<Mission> subMissions = new HashSet<Mission>();
            bool shouldCloseConnection = false;

            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oidParentMission = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidMission);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Mission subMission = new Mission()
                            {
                                // Activite base properties
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = reader["dateCreation"] != DBNull.Value ? Convert.ToDateTime(reader["dateCreation"]) : DateTime.MinValue,
                                DateModification = reader["dateModification"] != DBNull.Value ? Convert.ToDateTime(reader["dateModification"]) : DateTime.MinValue,
                                DatePrevisionnelleDebut = reader["datePrevisionnelleDebut"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleDebut"]) : DateTime.MinValue,
                                DatePrevisionnelleFin = reader["datePrevisionnelleFin"] != DBNull.Value ? Convert.ToDateTime(reader["datePrevisionnelleFin"]) : DateTime.MinValue,
                                DateEffectiveDebut = reader["dateEffectiveDebut"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveDebut"]) : DateTime.MinValue,
                                DateEffectiveFin = reader["dateEffectiveFin"] != DBNull.Value ? Convert.ToDateTime(reader["dateEffectiveFin"]) : DateTime.MinValue,
                                DomainePrincipal = null, // Avoid deep recursion - load separately if needed
                                Domaines = new HashSet<DomaineMetier>(), // Empty to avoid deep recursion

                                // Mission specific properties
                                TypeMission = null, // Avoid deep recursion - load separately if needed
                                ParentMission = null, // Avoid deep recursion
                                SubMissions = new HashSet<Mission>() // Empty to avoid deep recursion
                            };
                            subMissions.Add(subMission);
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return subMissions;
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var mission = entity as Mission;
            if (mission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO Mission (oid, __version, code, libelle, description, dateCreation, dateModification, datePrevisionnelleDebut, datePrevisionnelleFin, dateEffectiveDebut, dateEffectiveFin, oidDomainePrincipal, oidTypeMission, oidParentMission)
                              VALUES (@oid, @__version, @code, @libelle, @description, @dateCreation, @dateModification, @datePrevisionnelleDebut, @datePrevisionnelleFin, @dateEffectiveDebut, @dateEffectiveFin, @oidDomainePrincipal, @oidTypeMission, @oidParentMission)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    // Activite base properties
                    command.Parameters.AddWithValue("@oid", mission.Oid);
                    command.Parameters.AddWithValue("@__version", mission.__version);
                    command.Parameters.AddWithValue("@code", mission.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", mission.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", mission.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", mission.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", mission.DateModification);
                    command.Parameters.AddWithValue("@datePrevisionnelleDebut", mission.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@datePrevisionnelleFin", mission.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@dateEffectiveDebut", mission.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@dateEffectiveFin", mission.DateEffectiveFin);
                    command.Parameters.AddWithValue("@oidDomainePrincipal", mission.DomainePrincipal?.Oid ?? (object)DBNull.Value);

                    // Mission specific properties
                    command.Parameters.AddWithValue("@oidTypeMission", mission.TypeMission?.Oid ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidParentMission", mission.ParentMission?.Oid ?? (object)DBNull.Value);

                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var mission = entity as Mission;
            if (mission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE Mission SET __version=@__version, code=@code, libelle=@libelle, description=@description, dateCreation=@dateCreation, dateModification=@dateModification, datePrevisionnelleDebut=@datePrevisionnelleDebut, datePrevisionnelleFin=@datePrevisionnelleFin, dateEffectiveDebut=@dateEffectiveDebut, dateEffectiveFin=@dateEffectiveFin, oidDomainePrincipal=@oidDomainePrincipal, oidTypeMission=@oidTypeMission, oidParentMission=@oidParentMission WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    // Activite base properties
                    command.Parameters.AddWithValue("@oid", mission.Oid);
                    command.Parameters.AddWithValue("@__version", mission.__version);
                    command.Parameters.AddWithValue("@code", mission.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", mission.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", mission.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", mission.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", mission.DateModification);
                    command.Parameters.AddWithValue("@datePrevisionnelleDebut", mission.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@datePrevisionnelleFin", mission.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@dateEffectiveDebut", mission.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@dateEffectiveFin", mission.DateEffectiveFin);
                    command.Parameters.AddWithValue("@oidDomainePrincipal", mission.DomainePrincipal?.Oid ?? (object)DBNull.Value);

                    // Mission specific properties
                    command.Parameters.AddWithValue("@oidTypeMission", mission.TypeMission?.Oid ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidParentMission", mission.ParentMission?.Oid ?? (object)DBNull.Value);

                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            var mission = entity as Mission;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Mission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", mission.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Mission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }
    }
}