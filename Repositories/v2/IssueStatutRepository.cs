using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueStatutRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }
            HashSet<IssueStatut> issueStatuts = new HashSet<IssueStatut>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssueStatut", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IssueStatut issueStatut = new IssueStatut()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                Nouveau = Convert.ToBoolean(reader["nouveau"]),
                                Ferme = Convert.ToBoolean(reader["ferme"]),
                                DomainesMetier = GetDomainesMetierForIssueStatut(Convert.ToInt64(reader["oid"]))
                            };

                            issueStatuts.Add(issueStatut);
                        }
                    }
                }

                return issueStatuts.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssueStatut WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            IssueStatut issueStatut = new IssueStatut()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                Nouveau = Convert.ToBoolean(reader["nouveau"]),
                                Ferme = Convert.ToBoolean(reader["ferme"]),
                                DomainesMetier = GetDomainesMetierForIssueStatut(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)issueStatut;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueStatut = entity as IssueStatut;
            if (issueStatut == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO IssueStatut (oid, __version, code, libelle, description, obsolete, nouveau, ferme) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete, @nouveau, @ferme)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueStatut.Oid);
                    command.Parameters.AddWithValue("@__version", issueStatut.__version);
                    command.Parameters.AddWithValue("@code", issueStatut.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueStatut.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueStatut.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueStatut.Obsolete);
                    command.Parameters.AddWithValue("@nouveau", issueStatut.Nouveau);
                    command.Parameters.AddWithValue("@ferme", issueStatut.Ferme);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueStatut = entity as IssueStatut;
            if (issueStatut == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE IssueStatut SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete, nouveau=@nouveau, ferme=@ferme WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueStatut.Oid);
                    command.Parameters.AddWithValue("@__version", issueStatut.__version);
                    command.Parameters.AddWithValue("@code", issueStatut.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueStatut.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueStatut.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueStatut.Obsolete);
                    command.Parameters.AddWithValue("@nouveau", issueStatut.Nouveau);
                    command.Parameters.AddWithValue("@ferme", issueStatut.Ferme);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }
            var issueStatut = entity as IssueStatut;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssueStatut WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issueStatut.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(IssueStatut))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueStatut).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssueStatut WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForIssueStatut(long oidIssueStatut)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN IssueStatut_DomaineMetier isdm ON dm.oid = isdm.oidDomaineMetier WHERE isdm.oidIssueStatut = @oidIssueStatut", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oidIssueStatut", oidIssueStatut);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
        }
    }
}
