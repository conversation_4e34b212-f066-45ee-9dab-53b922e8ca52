using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class NiveauComplexiteRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }
            HashSet<NiveauComplexite> niveauxComplexite = new HashSet<NiveauComplexite>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM NiveauComplexite", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            NiveauComplexite niveauComplexite = new NiveauComplexite()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForNiveauComplexite(Convert.ToInt64(reader["oid"]))
                            };

                            niveauxComplexite.Add(niveauComplexite);
                        }
                    }
                }

                return niveauxComplexite.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM NiveauComplexite WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            NiveauComplexite niveauComplexite = new NiveauComplexite()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForNiveauComplexite(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)niveauComplexite;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var niveauComplexite = entity as NiveauComplexite;
            if (niveauComplexite == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO NiveauComplexite (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", niveauComplexite.Oid);
                    command.Parameters.AddWithValue("@__version", niveauComplexite.__version);
                    command.Parameters.AddWithValue("@code", niveauComplexite.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", niveauComplexite.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", niveauComplexite.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", niveauComplexite.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var niveauComplexite = entity as NiveauComplexite;
            if (niveauComplexite == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE NiveauComplexite SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", niveauComplexite.Oid);
                    command.Parameters.AddWithValue("@__version", niveauComplexite.__version);
                    command.Parameters.AddWithValue("@code", niveauComplexite.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", niveauComplexite.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", niveauComplexite.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", niveauComplexite.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }
            var niveauComplexite = entity as NiveauComplexite;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM NiveauComplexite WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", niveauComplexite.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(NiveauComplexite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(NiveauComplexite).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM NiveauComplexite WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForNiveauComplexite(long oidNiveauComplexite)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN NiveauComplexite_DomaineMetier ncdm ON dm.oid = ncdm.oidDomaineMetier WHERE ncdm.oidNiveauComplexite = @oidNiveauComplexite", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oidNiveauComplexite", oidNiveauComplexite);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
        }
    }
}
