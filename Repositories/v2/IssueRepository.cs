using HeliosETL.Const;
using HeliosETL.Models.v2;
using Microsoft.Data.SqlClient;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }
            HashSet<Issue> issues = new HashSet<Issue>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Issues", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Issue issue = new Issue()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Sujet = reader["sujet"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                DatePrevisionnelleDebut = Convert.ToDateTime(reader["datePrevisionnelleDebut"]),
                                DatePrevisionnelleFin = Convert.ToDateTime(reader["datePrevisionnelleFin"]),
                                DateEffectiveDebut = Convert.ToDateTime(reader["dateEffectiveDebut"]),
                                DateEffectiveFin = Convert.ToDateTime(reader["dateEffectiveFin"]),
                                KindOfActivite = reader["kindOfActivite"].ToString() ?? string.Empty,
                                KindOfIssueParente = reader["kindOfIssueParente"].ToString() ?? string.Empty,
                                Avancement = Convert.ToByte(reader["avancement"]),
                                TempsEstimeMinutes = Convert.ToInt32(reader["tempsEstimeMinutes"]),
                                TempsEffectifMinutes = Convert.ToInt32(reader["tempsEffectifMinutes"])
                            };

                            issues.Add(issue);
                        }
                    }
                }

                return issues.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Issues WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Issue issue = new Issue()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Sujet = reader["sujet"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                DatePrevisionnelleDebut = Convert.ToDateTime(reader["datePrevisionnelleDebut"]),
                                DatePrevisionnelleFin = Convert.ToDateTime(reader["datePrevisionnelleFin"]),
                                DateEffectiveDebut = Convert.ToDateTime(reader["dateEffectiveDebut"]),
                                DateEffectiveFin = Convert.ToDateTime(reader["dateEffectiveFin"]),
                                KindOfActivite = reader["kindOfActivite"].ToString() ?? string.Empty,
                                KindOfIssueParente = reader["kindOfIssueParente"].ToString() ?? string.Empty,
                                Avancement = Convert.ToByte(reader["avancement"]),
                                TempsEstimeMinutes = Convert.ToInt32(reader["tempsEstimeMinutes"]),
                                TempsEffectifMinutes = Convert.ToInt32(reader["tempsEffectifMinutes"])
                            };
                            return (T)(object)issue;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issue = entity as Issue;
            if (issue == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO Issues (oid, __version, code, sujet, description, dateCreation, dateModification, datePrevisionnelleDebut, datePrevisionnelleFin, dateEffectiveDebut, dateEffectiveFin, kindOfActivite, kindOfIssueParente, avancement, tempsEstimeMinutes, tempsEffectifMinutes) VALUES (@oid, @__version, @code, @sujet, @description, @dateCreation, @dateModification, @datePrevisionnelleDebut, @datePrevisionnelleFin, @dateEffectiveDebut, @dateEffectiveFin, @kindOfActivite, @kindOfIssueParente, @avancement, @tempsEstimeMinutes, @tempsEffectifMinutes)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issue.Oid);
                    command.Parameters.AddWithValue("@__version", issue.__version);
                    command.Parameters.AddWithValue("@code", issue.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@sujet", issue.Sujet ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issue.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", issue.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", issue.DateModification);
                    command.Parameters.AddWithValue("@datePrevisionnelleDebut", issue.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@datePrevisionnelleFin", issue.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@dateEffectiveDebut", issue.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@dateEffectiveFin", issue.DateEffectiveFin);
                    command.Parameters.AddWithValue("@kindOfActivite", issue.KindOfActivite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@kindOfIssueParente", issue.KindOfIssueParente ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@avancement", issue.Avancement);
                    command.Parameters.AddWithValue("@tempsEstimeMinutes", issue.TempsEstimeMinutes);
                    command.Parameters.AddWithValue("@tempsEffectifMinutes", issue.TempsEffectifMinutes);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issue = entity as Issue;
            if (issue == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE Issues SET __version=@__version, code=@code, sujet=@sujet, description=@description, dateCreation=@dateCreation, dateModification=@dateModification, datePrevisionnelleDebut=@datePrevisionnelleDebut, datePrevisionnelleFin=@datePrevisionnelleFin, dateEffectiveDebut=@dateEffectiveDebut, dateEffectiveFin=@dateEffectiveFin, kindOfActivite=@kindOfActivite, kindOfIssueParente=@kindOfIssueParente, avancement=@avancement, tempsEstimeMinutes=@tempsEstimeMinutes, tempsEffectifMinutes=@tempsEffectifMinutes WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issue.Oid);
                    command.Parameters.AddWithValue("@__version", issue.__version);
                    command.Parameters.AddWithValue("@code", issue.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@sujet", issue.Sujet ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issue.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", issue.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", issue.DateModification);
                    command.Parameters.AddWithValue("@datePrevisionnelleDebut", issue.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@datePrevisionnelleFin", issue.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@dateEffectiveDebut", issue.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@dateEffectiveFin", issue.DateEffectiveFin);
                    command.Parameters.AddWithValue("@kindOfActivite", issue.KindOfActivite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@kindOfIssueParente", issue.KindOfIssueParente ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@avancement", issue.Avancement);
                    command.Parameters.AddWithValue("@tempsEstimeMinutes", issue.TempsEstimeMinutes);
                    command.Parameters.AddWithValue("@tempsEffectifMinutes", issue.TempsEffectifMinutes);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }
            var issue = entity as Issue;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Issues WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", issue.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Issue))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Issue).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Issues WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }
    }
}