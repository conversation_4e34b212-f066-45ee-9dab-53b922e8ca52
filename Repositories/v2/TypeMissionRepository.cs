
using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypeMissionRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }
            HashSet<TypeMission> typeMissions = new HashSet<TypeMission>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM TypeMission", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TypeMission typeMission = new TypeMission()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = this.GetDomaineMetierForTypeMission(Convert.ToInt32(reader["oid"]))
                            };

                            typeMissions.Add(typeMission);
                        }
                    }
                }

                return typeMissions.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM TypeMission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            TypeMission typeMission = new TypeMission()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = this.GetDomaineMetierForTypeMission(Convert.ToInt32(reader["oid"]))
                            };
                            return (T)(object)typeMission;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public HashSet<DomaineMetier> GetDomaineMetierForTypeMission(long oid)
        {
            if(oid <= 0)
            {
                throw new ArgumentException("Invalid id. Expected positive integer.");
            }
    
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            bool shouldCloseConnection = false;
    
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }
        
                using (var command = new MySqlCommand(
                           "SELECT * FROM DomaineMetier WHERE oid IN (SELECT oidDomaineMetier FROM TypeMission_DomaineMetier WHERE oidTypeMission = @oid)",
                           this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oid);
            
                    using(var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };

                            domainesMetier.Add(domaineMetier);
                        }
                    }
                }
            } 
            catch (Exception)
            {
                if (shouldCloseConnection)
                {
                    this._db.MySQLConn.Close();
                }
                throw;
            }
            finally
            {
                if (shouldCloseConnection)
                {
                    this._db.MySQLConn.Close();
                }
            }

            return domainesMetier;
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var typeMission = entity as TypeMission;
            if (typeMission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO TypeMission (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", typeMission.Oid);
                    command.Parameters.AddWithValue("@__version", typeMission.__version);
                    command.Parameters.AddWithValue("@code", typeMission.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeMission.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeMission.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeMission.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var typeMission = entity as TypeMission;
            if (typeMission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE TypeMission SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", typeMission.Oid);
                    command.Parameters.AddWithValue("@__version", typeMission.__version);
                    command.Parameters.AddWithValue("@code", typeMission.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeMission.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeMission.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeMission.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }
            var typeMission = entity as TypeMission;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM TypeMission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", typeMission.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(TypeMission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeMission).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM TypeMission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }
    }
}